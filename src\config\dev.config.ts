/**
 * 开发环境配置
 * Development Environment Configuration
 */

export const DevConfig = {
  // 临时绕过登录 - 仅用于开发调试
  // Temporary login bypass - for development debugging only
  // 生产环境请务必设置为 false
  // MUST be set to false in production
  TEMPORARY_BYPASS_LOGIN: true,
  
  // 是否显示调试信息
  // Whether to show debug information
  SHOW_DEBUG_INFO: true,
  
  // 是否启用mock数据
  // Whether to enable mock data
  ENABLE_MOCK: false
}

// 生产环境检查
// Production environment check
if (import.meta.env.PROD && DevConfig.TEMPORARY_BYPASS_LOGIN) {
  console.error('🚨 警告：生产环境中检测到登录绕过已启用，这是不安全的！')
  console.error('🚨 Warning: Login bypass detected in production environment, this is unsafe!')
}
