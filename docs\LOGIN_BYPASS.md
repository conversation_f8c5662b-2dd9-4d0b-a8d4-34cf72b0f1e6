# 登录绕过功能说明 / Login Bypass Documentation

## 概述 / Overview

为了方便开发调试，项目中添加了临时绕过登录的功能。此功能仅用于开发环境，生产环境中必须禁用。

A temporary login bypass feature has been added for development convenience. This feature is for development environment only and MUST be disabled in production.

## 使用方法 / Usage

### 启用/禁用绕过 / Enable/Disable Bypass

编辑 `src/config/dev.config.ts` 文件：

Edit the `src/config/dev.config.ts` file:

```typescript
export const DevConfig = {
  // 设置为 true 启用绕过，false 禁用
  // Set to true to enable bypass, false to disable
  TEMPORARY_BYPASS_LOGIN: true,  // 改为 false 禁用 / Change to false to disable
}
```

### 功能说明 / Features

当 `TEMPORARY_BYPASS_LOGIN` 设置为 `true` 时：

When `TEMPORARY_BYPASS_LOGIN` is set to `true`:

1. **路由守卫绕过** / **Router Guard Bypass**: 用户可以直接访问任何页面，无需登录
2. **API请求绕过** / **API Request Bypass**: HTTP请求不会检查token，不会因为缺少token而跳转到登录页
3. **Token过期处理** / **Token Expiry Handling**: 即使token过期也不会强制跳转到登录页

## 安全警告 / Security Warning

⚠️ **重要提醒 / IMPORTANT REMINDER**:

- 此功能仅用于开发调试 / This feature is for development debugging only
- 生产环境中必须设置为 `false` / MUST be set to `false` in production
- 系统会在生产环境中检测到此设置时显示警告 / System will show warnings when detected in production

## 恢复正常登录 / Restore Normal Login

要恢复正常的登录流程，只需将 `TEMPORARY_BYPASS_LOGIN` 设置为 `false` 即可。

To restore normal login flow, simply set `TEMPORARY_BYPASS_LOGIN` to `false`.

## 相关文件 / Related Files

- `src/config/dev.config.ts` - 配置文件 / Configuration file
- `src/router/router-guards.ts` - 路由守卫 / Router guards
- `src/api/axios.ts` - HTTP拦截器 / HTTP interceptors
